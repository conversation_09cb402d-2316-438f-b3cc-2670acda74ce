#!/usr/bin/env node

import { execSync } from 'child_process';
import fs from 'fs';

console.log('🔍 验证 Monorepo 设置...\n');

const tests = [
  {
    name: '检查 pnpm 版本',
    command: 'pnpm --version',
    expected: /^\d+\.\d+\.\d+$/
  },
  {
    name: '检查工作区包',
    command: 'pnpm list --recursive --depth=0 --json',
    test: (output) => {
      try {
        const data = JSON.parse(output);
        const packages = data.filter(pkg => pkg.name && pkg.name.startsWith('@crypto/'));
        return packages.length === 2;
      } catch {
        return false;
      }
    }
  },
  {
    name: '检查前端项目配置',
    test: () => {
      try {
        const pkg = JSON.parse(fs.readFileSync('packages/admin/package.json', 'utf8'));
        return pkg.name === '@crypto/admin' && pkg.scripts && pkg.scripts.dev;
      } catch {
        return false;
      }
    }
  },
  {
    name: '检查后端项目配置',
    test: () => {
      try {
        const pkg = JSON.parse(fs.readFileSync('packages/node/package.json', 'utf8'));
        return pkg.name === '@crypto/node' && pkg.scripts && pkg.scripts.dev;
      } catch {
        return false;
      }
    }
  }
];

let passed = 0;
let failed = 0;

for (const test of tests) {
  try {
    let result = false;
    
    if (test.command) {
      const output = execSync(test.command, { encoding: 'utf8', stdio: 'pipe' });
      if (test.expected) {
        result = test.expected.test(output.trim());
      } else if (test.test) {
        result = test.test(output);
      } else {
        result = true;
      }
    } else if (test.test) {
      result = test.test();
    }
    
    if (result) {
      console.log(`✅ ${test.name}`);
      passed++;
    } else {
      console.log(`❌ ${test.name}`);
      failed++;
    }
  } catch (error) {
    console.log(`❌ ${test.name} - 错误: ${error.message}`);
    failed++;
  }
}

console.log(`\n📊 测试结果: ${passed} 通过, ${failed} 失败`);

if (failed === 0) {
  console.log('\n🎉 所有测试通过！Monorepo 设置正确。');
  console.log('\n📖 下一步:');
  console.log('  1. 运行 pnpm dev 启动开发环境');
  console.log('  2. 查看 SETUP_GUIDE.md 了解详细使用方法');
} else {
  console.log('\n⚠️  有些测试失败，请检查配置。');
  console.log('  查看 SETUP_GUIDE.md 中的故障排除部分');
}
