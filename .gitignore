# Dependencies
node_modules/
.pnpm-store/

# Build outputs
dist/
build/
.next/
.nuxt/
.output/
.vite/
dist-ssr/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo
.tsbuildinfo.*

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache
cache/

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Turbo
.turbo/

# IDE
.vscode/
.idea/
.project
*.swp
*.swo
*.sw*
*.suo
*.ntvs*
*.njsproj
*.sln
*.un~
launch.json

# OS
.DS_Store
Thumbs.db

# Temporary folders
tmp/
temp/
run/

# Lock files (we use pnpm, but keep pnpm-lock.yaml for sub-packages)
package-lock.json
yarn.lock

# Vite specific
vite.config.ts.timestamp*

# Project specific
data/*
public/uploads/*
