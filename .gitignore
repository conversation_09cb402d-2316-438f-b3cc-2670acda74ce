.DS_Store
node_modules/
/dist/
dist-ssr/

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.project
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw*

vite.config.ts.timestamp*

logs/
cache/
npm-debug.log
yarn-error.log
node_modules/
package-lock.json
yarn.lock
coverage/
dist/
.idea/
run/
build/
.DS_Store
launch.json
*.sw*
*.un~
.tsbuildinfo
.tsbuildinfo.*
data/*
pnpm-lock.yaml
public/uploads/*
