# Crypto Monorepo

基于 pnpm workspace + Turborepo 的 Monorepo 架构项目。

## 项目结构

```
crypto/
├── packages/
│   ├── admin/          # 前端管理系统 (Vue 3 + TypeScript)
│   └── node/           # 后端 API 服务 (Node.js + Midway.js)
├── package.json        # 根目录依赖管理
├── pnpm-workspace.yaml # pnpm 工作区配置
├── turbo.json          # Turborepo 配置
├── eslint.config.js    # ESLint 配置 (基于 @antfu/eslint-config)
├── tsconfig.json       # TypeScript 基础配置
├── .prettierrc.json    # Prettier 配置
└── .editorconfig       # 编辑器配置
```

## 技术栈

### 前端 (Admin)
- **框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **UI 库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **样式**: Tailwind CSS + SCSS

### 后端 (Node)
- **框架**: Midway.js + Koa.js
- **语言**: TypeScript
- **数据库**: TypeORM (支持 MySQL/PostgreSQL/SQLite)
- **测试**: Jest

### 开发工具
- **包管理**: pnpm
- **构建系统**: Turborepo
- **代码规范**: ESLint (@antfu/eslint-config) + Prettier
- **Git 钩子**: Husky + lint-staged

## 快速开始

### 环境要求

- Node.js >= 18.0.0
- pnpm >= 8.0.0

### 安装依赖

```bash
# 安装所有依赖
pnpm install
```

### 开发模式

```bash
# 启动所有项目的开发模式
pnpm dev

# 或者单独启动某个项目
pnpm --filter @crypto/admin dev    # 启动前端
pnpm --filter @crypto/node dev     # 启动后端
```

### 构建项目

```bash
# 构建所有项目
pnpm build

# 构建特定项目
pnpm --filter @crypto/admin build
pnpm --filter @crypto/node build
```

### 代码检查和格式化

```bash
# 检查所有项目的代码规范
pnpm lint

# 自动修复代码规范问题
pnpm lint:fix

# 格式化代码
pnpm format

# 类型检查
pnpm type-check
```

### 测试

```bash
# 运行所有测试
pnpm test

# 运行特定项目的测试
pnpm --filter @crypto/node test
```

## 项目命令

### 根目录命令

| 命令 | 描述 |
|------|------|
| `pnpm dev` | 启动所有项目的开发模式 |
| `pnpm build` | 构建所有项目 |
| `pnpm lint` | 检查代码规范 |
| `pnpm lint:fix` | 自动修复代码规范问题 |
| `pnpm format` | 格式化代码 |
| `pnpm type-check` | TypeScript 类型检查 |
| `pnpm test` | 运行所有测试 |
| `pnpm clean` | 清理构建产物和依赖 |

### 子项目命令

使用 `pnpm --filter <package-name> <command>` 来运行特定项目的命令：

```bash
# 前端项目
pnpm --filter @crypto/admin dev
pnpm --filter @crypto/admin build
pnpm --filter @crypto/admin preview

# 后端项目
pnpm --filter @crypto/node dev
pnpm --filter @crypto/node build
pnpm --filter @crypto/node start
pnpm --filter @crypto/node test
```

## 开发规范

### 代码风格

项目使用 [@antfu/eslint-config](https://github.com/antfu/eslint-config) 作为代码规范，主要特点：

- 使用 2 空格缩进
- 不使用分号
- 使用单引号
- 尾随逗号 (ES5)
- 自动格式化

### 提交规范

项目配置了 Husky 和 lint-staged，在提交代码时会自动：

1. 运行 ESLint 检查和自动修复
2. 运行 Prettier 格式化
3. 进行 TypeScript 类型检查

### 目录结构规范

- 文件和组件命名使用 kebab-case (短横线连接)
- TypeScript 类型定义文件使用 `.d.ts` 后缀
- 测试文件使用 `.test.ts` 或 `.spec.ts` 后缀

## 部署

### 前端部署

```bash
# 构建前端项目
pnpm --filter @crypto/admin build

# 构建产物在 packages/admin/dist 目录
```

### 后端部署

```bash
# 构建后端项目
pnpm --filter @crypto/node build

# 启动生产环境
pnpm --filter @crypto/node start

# 或使用 PM2
pnpm --filter @crypto/node pm2:start
```

## 故障排除

### 依赖问题

```bash
# 清理所有 node_modules 和锁文件
pnpm clean

# 重新安装依赖
pnpm install
```

### 构建缓存问题

```bash
# 清理 Turborepo 缓存
rm -rf .turbo

# 清理所有构建产物
pnpm clean
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

MIT License
