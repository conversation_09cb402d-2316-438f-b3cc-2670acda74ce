#!/bin/bash

work_dir="/Users/<USER>/Desktop/Demo/crypto"

# 克隆 cool-admin-node-v8 到指定文件夹
cd "$work_dir"

# 先检查工作区是否有修改，如果有则先提交或存储
if [[ $(git status --porcelain) ]]; then
    echo "工作区有未提交的修改，先进行处理..."
    # 选项1：自动提交当前修改
    git add .
    git commit -m "自动提交：执行subtree前的更改"
    # 选项2（已注释）：如果不想提交，可以使用stash
    # git stash
fi

# 检查node目录是否已存在，决定是添加还是拉取
if [ -d "packages/node" ]; then
    echo "packages/node 已存在，执行拉取更新..."
    git subtree pull --prefix=packages/node https://gitee.com/cool-team-official/cool-admin-midway 8.x --squash
else
    echo "packages/node 不存在，执行添加..."
    git subtree add --prefix=packages/node https://gitee.com/cool-team-official/cool-admin-midway 8.x --squash
fi

# 检查admin目录是否已存在，决定是添加还是拉取
if [ -d "packages/admin" ]; then
    echo "packages/admin 已存在，执行拉取更新..."
    git subtree pull --prefix=packages/admin https://gitee.com/cool-team-official/cool-admin-vue.git 8.x --squash
else
    echo "packages/admin 不存在，执行添加..."
    git subtree add --prefix=packages/admin https://gitee.com/cool-team-official/cool-admin-vue.git 8.x --squash
fi
