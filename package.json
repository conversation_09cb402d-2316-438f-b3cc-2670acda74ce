{"name": "crypto-monorepo", "version": "1.0.0", "private": true, "description": "Crypto项目 - 使用pnpm workspace + Turborepo的Monorepo架构", "type": "module", "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "lint:fix": "turbo lint:fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,vue,json,md}\"", "type-check": "turbo type-check", "test": "turbo test", "clean": "turbo clean && rm -rf node_modules", "verify": "node scripts/verify-setup.js"}, "devDependencies": {"@antfu/eslint-config": "^4.13.2", "@types/node": "^22.10.5", "eslint": "^9.19.0", "husky": "^9.1.7", "lint-staged": "^15.2.11", "prettier": "^3.4.2", "prettier-eslint": "^16.4.2", "turbo": "^2.3.4", "typescript": "^5.8.2"}, "packageManager": "pnpm@9.15.2", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "lint-staged": {"*.{js,jsx,ts,tsx,vue}": ["eslint --fix"], "*.{js,jsx,ts,tsx,vue,json,md}": ["prettier --write"]}}