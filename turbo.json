{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", "build/**", ".next/**", "!.next/cache/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^lint"]}, "lint:fix": {"dependsOn": ["^lint:fix"]}, "type-check": {"dependsOn": ["^type-check"]}, "test": {"dependsOn": ["^test"], "outputs": ["coverage/**"]}, "clean": {"cache": false}}}