import antfu from '@antfu/eslint-config'

export default antfu(
  {
    // Node.js 后端项目配置
    typescript: true,
    vue: false, // 后端项目不需要 Vue 支持
    formatters: {
      markdown: 'prettier',
    },
    // 忽略的文件和目录
    ignores: [
      '**/dist/**',
      '**/build/**',
      '**/node_modules/**',
      '**/coverage/**',
      '**/logs/**',
      '**/public/**',
      '**/.turbo/**',
      'bootstrap.js',
    ],
  },
  {
    // Node.js 项目特定规则
    rules: {
      // 允许 console 语句（服务端日志）
      'no-console': 'off',
      
      // TypeScript 规则
      '@typescript-eslint/no-explicit-any': 'warn',
      '@typescript-eslint/ban-ts-comment': 'warn',
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_',
        },
      ],
      
      // Node.js 相关
      'node/prefer-global/process': 'off',
      'node/prefer-global/buffer': 'off',
      
      // 导入规则
      'import/order': [
        'error',
        {
          groups: [
            'builtin',
            'external',
            'internal',
            'parent',
            'sibling',
            'index',
          ],
          'newlines-between': 'always',
        },
      ],
    },
  },
)
