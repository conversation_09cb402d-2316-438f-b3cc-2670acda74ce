{"name": "@crypto/node", "version": "8.0.0", "description": "一个很酷的Ai快速开发框架", "private": true, "dependencies": {"@cool-midway/core": "8.0.4", "@cool-midway/rpc": "8.0.1", "@cool-midway/task": "8.0.2", "@midwayjs/bootstrap": "^3.20.3", "@midwayjs/cache-manager": "^3.20.3", "@midwayjs/core": "^3.20.3", "@midwayjs/cron": "^3.20.3", "@midwayjs/cross-domain": "^3.20.3", "@midwayjs/info": "^3.20.3", "@midwayjs/koa": "^3.20.3", "@midwayjs/logger": "^3.4.2", "@midwayjs/static-file": "^3.20.3", "@midwayjs/typeorm": "^3.20.3", "@midwayjs/upload": "^3.20.3", "@midwayjs/validate": "^3.20.3", "adm-zip": "^0.5.16", "axios": "^1.8.4", "cron": "^4.1.3", "download": "^8.0.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "md5": "^2.3.0", "moment": "^2.30.1", "mysql2": "^3.14.0", "pg": "^8.16.0", "svg-captcha": "^1.4.0", "tslib": "^2.8.1", "typeorm": "npm:@cool-midway/typeorm@0.3.20", "uuid": "^11.1.0", "ws": "^8.18.1"}, "devDependencies": {"@midwayjs/bundle-helper": "^1.3.0", "@midwayjs/mock": "^3.20.3", "@types/jest": "^29.5.14", "@types/node": "22", "@yao-pkg/pkg": "^6.3.2", "cross-env": "^7.0.3", "jest": "^29.7.0", "mwts": "^1.3.0", "mwtsc": "^1.15.1", "rimraf": "^6.0.1", "ts-jest": "^29.3.0", "typescript": "~5.8.2"}, "engines": {"node": ">=18.0.0"}, "scripts": {"start": "NODE_ENV=production node ./bootstrap.js", "dev": "rimraf src/index.ts && cool check && cross-env NODE_ENV=local mwtsc --cleanOutDir --watch --run @midwayjs/mock/app.js --keepalive", "test": "cross-env NODE_ENV=unittest jest", "cov": "jest --coverage", "lint": "mwts check", "lint:fix": "mwts fix", "type-check": "tsc --noEmit", "ci": "npm run cov", "build": "cool entity && bundle && mwtsc --cleanOutDir", "build:obfuscate": "cool entity && bundle && mwtsc --cleanOutDir && cool obfuscate", "pkg": "rimraf build && mkdir build && npm run build && pkg . -d > build/pkg.log", "pm2:start": "pm2 start ./bootstrap.js -i 1 --name cool-admin", "pm2:stop": "pm2 stop cool-admin & pm2 delete cool-admin", "clean": "rimraf dist build .turbo node_modules/.cache"}, "bin": "./bootstrap.js", "pkg": {"scripts": ["dist/**/*", "node_modules/axios/dist/node/*"], "assets": ["public/**/*", "typings/**/*", "src/locales/**/*"], "targets": ["node20-win-x64"], "outputPath": "build"}, "repository": {"type": "git", "url": "https://cool-js.com"}, "author": "COOL", "license": "MIT"}