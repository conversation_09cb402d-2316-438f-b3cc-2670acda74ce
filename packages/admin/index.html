<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="referer" content="never" />
		<meta name="renderer" content="webkit" />
		<meta
			name="viewport"
			content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=0"
		/>
		<title></title>
		<link rel="icon" href="./favicon.ico" />
		<style>
			html,
			body,
			#app {
				height: 100%;
				overflow: hidden;
			}

			* {
				-webkit-font-smoothing: antialiased;
				font-family:
					'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
					'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
			}

			.preload__wrap {
				display: flex;
				flex-direction: column;
				letter-spacing: 1px;
				background-color: #2f3447;
				position: fixed;
				left: 0;
				top: 0;
				height: 100%;
				width: 100%;
				z-index: 9999;
				transition: all 0.3s ease-in;
				opacity: 1;
				pointer-events: none;
			}

			.preload__wrap.is-hide {
				opacity: 0;
			}

			.preload__container {
				display: flex;
				justify-content: center;
				align-items: center;
				flex-direction: column;
				width: 100%;
				user-select: none;
				-webkit-user-select: none;
				flex-grow: 1;
			}

			.preload__name {
				font-size: 30px;
				color: #fff;
				letter-spacing: 5px;
				font-weight: bold;
				margin-bottom: 30px;
				min-height: 50px;
			}

			.preload__title {
				color: #fff;
				font-size: 14px;
				margin: 30px 0 20px 0;
				min-height: 20px;
			}

			.preload__sub-title {
				color: #ababab;
				font-size: 12px;
				min-height: 20px;
			}

			.preload__name,
			.preload__title,
			.preload__sub-title {
				animation: s 0.5s ease-in;
			}

			@keyframes s {
				from {
					opacity: 0;
				}
				to {
					opacity: 1;
				}
			}

			.preload__loading {
				height: 44px;
				width: 44px;
				border-radius: 30px;
				border: 7px solid currentColor;
				border-bottom-color: #2f3447;
				position: relative;
				animation:
					r 1s infinite cubic-bezier(0.17, 0.67, 0.83, 0.67),
					bc 2s infinite ease-in;
				transform: rotate(0deg);
				box-sizing: border-box;
			}

			@keyframes r {
				from {
					transform: rotate(0deg);
				}

				to {
					transform: rotate(360deg);
				}
			}

			.preload__loading::after,
			.preload__loading::before {
				content: '';
				display: inline-block;
				position: absolute;
				bottom: -2px;
				height: 7px;
				width: 7px;
				border-radius: 10px;
				background-color: currentColor;
			}

			.preload__loading::after {
				left: -1px;
			}

			.preload__loading::before {
				right: -1px;
			}

			@keyframes bc {
				0% {
					color: #689cc5;
				}

				25% {
					color: #b3b7e2;
				}

				50% {
					color: #93dbe9;
				}

				75% {
					color: #abbd81;
				}

				100% {
					color: #689cc5;
				}
			}
		</style>
	</head>

	<body>
		<div class="preload__wrap" id="Loading">
			<div class="preload__container">
				<div class="preload__name"></div>
				<div class="preload__loading"></div>
				<div class="preload__title"></div>
				<div class="preload__sub-title"></div>
			</div>
		</div>

		<div id="app"></div>
		<script type="module" src="/src/main.ts"></script>
	</body>
</html>
