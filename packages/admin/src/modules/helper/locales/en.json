{"请选择图标": "Please select an icon", "路由缓存": "Routing cache", "开始创建": "Start creating", "AI极速编码": "AI Coding", "搜索插件名称": "Search plugin name", "后端": "Backend", "未知": "Unknown", "暂无描述": "No description available", "示例": "Example", "预览": "Preview", "文档": "Documentation", "图片预览": "Image preview", "说明文档": "Instruction document", "作者": "Author", "更新时间": "Update time", "格式化": "Formatting", "插入文件链接": "Insert file link", "已安装": "Installed", "全部插件": "All plugins", "插件开发": "Plugin development", "确定要卸载插件【{name}】吗？": "Are you sure you want to uninstall the plugin [{name}]?", "提示": "Prompt", "卸载成功": "Uninstallation successful", "启用成功": "Enablement successful", "禁用成功": "Disable successfully", "参数格式错误": "Parameter format error", "设置": "Settings", "参数": "Parameter", "修改成功": "Modify successfully", "确定要退出编码吗？": "Are you sure you want to exit the encoding?", "创建目录：{name}": "Create directory: {name}", "创建菜单：{name}": "Create menu: {name}", "创建 Java 文件": "Create Java file", "创建 Node 文件": "Create Node file", "正在重启服务": "Restarting service", "创建 Vue 文件": "Create Vue file", "检测到插件「{name}」，是否安装？": "Plugin 「{name}」 detected. Do you want to install it?", "安装": "Install", "插件「{name}」安装成功": "Plugin 「{name}」 installed successfully", "点击查看": "Click to view", "继续": "Continue", "自动添加": "Automatically add", "权限名称": "Permission name", "实体数据": "Entity data", "自动添加权限": "Automatically add permissions", "一键添加": "Add in one click", "权限列表": "Permission list", "请选择实体数据": "Please select entity data", "请填写权限名称": "Please fill in the permission name", "请至少选择一个权限": "Please select at least one permission", "添加权限成功": "Successfully added permissions", "快速创建": "Quick creation", "请选择数据结构": "Please select the data structure", "数据结构": "Data structure", "上级节点": "Parent node", "请选择上级节点": "Please select the parent node", "菜单名称": "Menu name", "请输入菜单名称": "Please enter the menu name", "菜单路由": "Menu route", "请输入菜单路由，如：/test": "Please enter the menu route, e.g.: /test", "必须以 / 开头": "Must start with /", "菜单排序": "Menu sorting", "请填写菜单排序": "Please fill in the menu sorting", "菜单图标": "Menu icon"}