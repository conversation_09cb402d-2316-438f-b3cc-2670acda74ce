# 介绍

**cool-admin for vue**是基于[Vue.js](https://v3.cn.vuejs.org)开发。

[cool-admin 官方文档](https://cool-js.com/)

尝试 `cool-admin` 最简单的方法就是查看文档及运行示例。

<img src='https://vue.cool-admin.com/show/admin.png' />

[Ai极速编码 🔥 在线体验](https://show.cool-admin.com/helper/ai-code)

<img src='https://vue.cool-admin.com/show/code.png' />

## 代码仓库

**cool-admin for vue** 是开源免费的，遵循[MIT](https://baike.baidu.com/item/MIT/10772952)开源协议，意味着您无需支付任何费用，也无需授权，即可将它应用到您的产品中。

开源免费，并不意味着您可以将 cool-admin 应用到非法的领域，比如涉及赌博，暴力等方面。如因此产生纠纷等法律问题，`cool-admin`不承担任何责任。

[https://github.com/cool-team-official/cool-admin-vue](https://github.com/cool-team-official/cool-admin-vue)

```shell
git clone https://github.com/cool-team-official/cool-admin-vue.git
```

## 技术选型

-   [Vue.js](https://v3.cn.vuejs.org)，基础框架；
-   [VueRouter](https://router.vuejs.org)，Vue.js 官方路由；
-   [Pinia](https://pinia.vuejs.org)，轻量级状态管理库；
-   [ElementPlus](https://element-plus.gitee.io/zh-CN)，桌面端组件库；
-   [Vite](https://vitejs.cn)，构建工具；
