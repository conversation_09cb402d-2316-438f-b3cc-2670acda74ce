{"cl-crud": {"prefix": "cl-crud", "scope": "vue", "body": ["<template>", "    <cl-crud ref=\"Crud\">", "        <cl-row>", "            <!-- 刷新按钮 -->", "            <cl-refresh-btn />", "            <!-- 新增按钮 -->", "            <cl-add-btn />", "            <!-- 删除按钮 -->", "            <cl-multi-delete-btn />", "            <cl-flex1 />", "            <!-- 条件搜索 -->", "            <cl-search ref=\"Search\" />", "        </cl-row>", "", "        <cl-row>", "            <!-- 数据表格 -->", "            <cl-table ref=\"Table\" />", "        </cl-row>", "", "        <cl-row>", "            <cl-flex1 />", "            <!-- 分页控件 -->", "            <cl-pagination />", "        </cl-row>", "", "        <!-- 新增、编辑 -->", "        <cl-upsert ref=\"Upsert\" />", "    </cl-crud>", "</template>", "", "<script lang=\"ts\" setup>", "defineOptions({", "    name: \"$1\"", "});", "", "import { useCrud, useTable, useUpsert, useSearch } from \"@cool-vue/crud\";", "import { useCool } from \"/@/cool\";", "", "const { service } = useCool();", "", "// cl-upsert", "const Upsert = useUpsert({", "    items: []", "});", "", "// cl-table", "const Table = useTable({", "    columns: []", "});", "", "// cl-search", "const Search = useSearch();", "", "// cl-crud", "const Crud = useCrud(", "    {", "        service: service$2", "    },", "    (app) => {", "        app.refresh();", "    }", ");", "", "// 刷新", "function refresh(params?: any) {", "    Crud.value?.refresh(params);", "}", "</script>", ""], "description": "cl-crud snippets"}, "cl-filter": {"prefix": "cl-filter", "scope": "html", "body": ["<cl-filter label=\"\">", "    <cl-select :options=\"[$1]\" prop=\"\" />", "</cl-filter>"], "description": "cl-filter snippets"}, "slot-item": {"prefix": "slot item", "scope": "html", "body": ["<template #slot-$1=\"{ scope }\">", "</template>", ""], "description": "slot snippets"}, "slot-column": {"prefix": "slot column", "scope": "html", "body": ["<template #column-$1=\"{ scope }\">", "    ", "</template>", ""], "description": "column slot snippets"}}