import antfu from '@antfu/eslint-config'

export default antfu(
	{
		// 继承根目录配置
		typescript: true,
		vue: true,
		formatters: {
			css: true,
			html: true,
			markdown: 'prettier',
		},
		// 忽略的文件和目录
		ignores: [
			'**/dist/**',
			'**/dist-ssr/**',
			'**/coverage/**',
			'**/packages/**',
			'**/build/**',
			'**/node_modules/**',
			'**/.turbo/**',
		],
	},
	{
		// Admin 项目特定规则
		rules: {
			// TypeScript 规则
			'@typescript-eslint/ban-ts-ignore': 'off',
			'@typescript-eslint/explicit-function-return-type': 'off',
			'@typescript-eslint/no-explicit-any': 'off',
			'@typescript-eslint/no-var-requires': 'off',
			'@typescript-eslint/no-empty-function': 'off',
			'@typescript-eslint/no-use-before-define': 'off',
			'@typescript-eslint/ban-ts-comment': 'off',
			'@typescript-eslint/ban-types': 'off',
			'@typescript-eslint/no-non-null-assertion': 'off',
			'@typescript-eslint/explicit-module-boundary-types': 'off',
			'@typescript-eslint/no-namespace': 'off',
			'@typescript-eslint/no-unused-vars': 'off',
			'@typescript-eslint/no-empty-object-type': 'off',

			// 通用规则
			'space-before-function-paren': 'off',
			'no-unused-vars': 'off',
			'no-use-before-define': 'off',
			'no-self-assign': 'off',

			// Vue 规则
			'vue/no-mutating-props': 'off',
			'vue/no-template-shadow': 'off',
			'vue/no-v-html': 'off',
			'vue/component-name-in-template-casing': ['error', 'kebab-case'],
			'vue/component-definition-name-casing': ['error', 'kebab-case'],
			'vue/attributes-order': 'off',
			'vue/one-component-per-file': 'off',
			'vue/html-closing-bracket-newline': 'off',
			'vue/max-attributes-per-line': 'off',
			'vue/multiline-html-element-content-newline': 'off',
			'vue/multi-word-component-names': 'off',
			'vue/singleline-html-element-content-newline': 'off',
			'vue/attribute-hyphenation': 'off',
			'vue/html-self-closing': 'off',
			'vue/require-default-prop': 'off',
			'vue/v-on-event-hyphenation': 'off',
			'vue/block-lang': 'off',
		},
	},
)
