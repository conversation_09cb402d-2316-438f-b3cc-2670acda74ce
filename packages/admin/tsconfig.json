{"extends": ["../../tsconfig.json", "@vue/tsconfig/tsconfig.dom.json"], "compilerOptions": {"target": "ESNext", "module": "ESNext", "experimentalDecorators": true, "verbatimModuleSyntax": false, "noImplicitAny": false, "baseUrl": ".", "types": ["./build/cool/eps", "./env", "@cool-vue/crud/index", "@cool-vue/vite-plugin/client", "element-plus/global"], "paths": {"/@/*": ["./src/*"], "/$/*": ["./src/modules/*"], "/#/*": ["./src/plugins/*"], "/~/*": ["./packages/*"], "@/*": ["./src/*"]}}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "env.d.ts"], "exclude": ["node_modules", "dist", "build", ".turbo"]}