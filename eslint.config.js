import antfu from '@antfu/eslint-config'

export default antfu(
  {
    // 启用 TypeScript 支持
    typescript: true,
    // 启用 Vue 支持
    vue: true,
    // 启用格式化规则
    formatters: {
      css: true,
      html: true,
      markdown: 'prettier',
    },
    // 忽略的文件和目录
    ignores: [
      '**/dist/**',
      '**/build/**',
      '**/node_modules/**',
      '**/coverage/**',
      '**/.turbo/**',
      '**/public/**',
      '**/logs/**',
    ],
  },
  {
    // 自定义规则
    rules: {
      // 允许 console 语句（开发阶段）
      'no-console': 'warn',
      // 允许未使用的变量（以下划线开头）
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_',
        },
      ],
      // Vue 相关规则
      'vue/multi-word-component-names': 'off',
      'vue/component-name-in-template-casing': ['error', 'kebab-case'],
      'vue/component-definition-name-casing': ['error', 'kebab-case'],
      // TypeScript 相关规则
      '@typescript-eslint/no-explicit-any': 'warn',
      '@typescript-eslint/ban-ts-comment': 'warn',
    },
  },
)
