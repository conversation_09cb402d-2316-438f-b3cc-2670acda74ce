# Crypto Monorepo 设置指南

## 🎉 恭喜！Monorepo 设置完成

您的项目已成功转换为使用 **pnpm workspace + Turborepo** 的 Monorepo 架构。

## 📁 项目结构

```
crypto/
├── packages/
│   ├── admin/          # 前端项目 (@crypto/admin)
│   └── node/           # 后端项目 (@crypto/node)
├── package.json        # 根目录依赖管理
├── pnpm-workspace.yaml # pnpm 工作区配置
├── turbo.json          # Turborepo 配置
├── eslint.config.js    # ESLint 配置 (基于 @antfu/eslint-config)
├── tsconfig.json       # TypeScript 基础配置
├── .prettierrc.json    # Prettier 配置
├── .editorconfig       # 编辑器配置
└── .gitignore          # Git 忽略文件
```

## 🚀 快速开始

### 1. 安装依赖
```bash
pnpm install
```

### 2. 启动开发环境

#### 启动所有项目
```bash
pnpm dev
```

#### 单独启动项目
```bash
# 启动前端项目
pnpm --filter @crypto/admin dev

# 启动后端项目
pnpm --filter @crypto/node dev
```

### 3. 构建项目

#### 构建所有项目
```bash
pnpm build
```

#### 单独构建项目
```bash
# 构建前端项目
pnpm --filter @crypto/admin build

# 构建后端项目
pnpm --filter @crypto/node build
```

## 🛠️ 开发工具

### 代码检查和格式化
```bash
# 检查代码规范
pnpm lint

# 自动修复代码规范问题
pnpm lint:fix

# 格式化代码
pnpm format

# TypeScript 类型检查
pnpm type-check
```

### 测试
```bash
# 运行所有测试
pnpm test

# 运行后端测试
pnpm --filter @crypto/node test
```

### 清理
```bash
# 清理构建产物和缓存
pnpm clean
```

## 📦 包管理

### 添加依赖

#### 添加到根目录（开发工具）
```bash
pnpm add -D <package-name>
```

#### 添加到特定项目
```bash
# 添加到前端项目
pnpm --filter @crypto/admin add <package-name>

# 添加到后端项目
pnpm --filter @crypto/node add <package-name>
```

#### 添加开发依赖
```bash
pnpm --filter @crypto/admin add -D <package-name>
```

### 移除依赖
```bash
pnpm --filter @crypto/admin remove <package-name>
```

## 🔧 配置说明

### ESLint 配置
- 使用 [@antfu/eslint-config](https://github.com/antfu/eslint-config)
- 支持 TypeScript 和 Vue
- 自动格式化功能
- 各子项目可以继承并自定义规则

### TypeScript 配置
- 根目录提供基础配置
- 各子项目继承根配置并可自定义
- 支持路径映射和装饰器

### Prettier 配置
- 统一的代码格式化规则
- 2 空格缩进，无分号，单引号

### Turborepo 配置
- 智能缓存和并行构建
- 任务依赖管理
- 增量构建支持

## 🚨 注意事项

### 1. 环境问题
如果遇到 Node.js 路径问题，可以尝试：
```bash
# 清理并重新安装
pnpm clean
pnpm install
```

### 2. 依赖冲突
如果遇到依赖版本冲突：
```bash
# 查看依赖树
pnpm list --depth=1

# 更新依赖
pnpm update
```

### 3. 缓存问题
如果构建有问题：
```bash
# 清理 Turborepo 缓存
rm -rf .turbo

# 清理所有缓存
pnpm clean
```

## 📝 开发规范

### 文件命名
- 使用 kebab-case (短横线连接)
- 组件文件：`user-profile.vue`
- 工具文件：`api-client.ts`

### 提交规范
- 使用语义化提交信息
- 格式：`type(scope): description`
- 例如：`feat(admin): add user management page`

### 代码风格
- 遵循 ESLint 规则
- 使用 Prettier 自动格式化
- TypeScript 严格模式

## 🔗 有用的命令

```bash
# 查看所有工作区包
pnpm list --recursive --depth=0

# 查看特定包的依赖
pnpm --filter @crypto/admin list

# 运行特定包的脚本
pnpm --filter @crypto/admin run <script-name>

# 在所有包中运行命令
pnpm -r run <script-name>

# 查看 Turborepo 任务
pnpm turbo run build --dry-run
```

## 🆘 故障排除

### 常见问题

1. **命令执行缓慢或卡住**
   - 检查 Node.js 版本 (需要 >= 18.0.0)
   - 清理缓存：`pnpm clean`

2. **TypeScript 编译错误**
   - 检查 tsconfig.json 配置
   - 确保路径映射正确

3. **ESLint 规则冲突**
   - 检查各项目的 eslint.config.js
   - 确保继承关系正确

4. **依赖安装失败**
   - 删除 node_modules 和 pnpm-lock.yaml
   - 重新运行 `pnpm install`

### 获取帮助
- 查看 [pnpm 文档](https://pnpm.io/)
- 查看 [Turborepo 文档](https://turbo.build/)
- 查看 [@antfu/eslint-config 文档](https://github.com/antfu/eslint-config)

---

🎉 **恭喜您成功设置了 Monorepo 架构！** 现在您可以享受统一的开发体验和高效的构建流程。
